#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 分析 checkbox 和 multi_select 的区别
 * 特别是 gmpexemptflag 和 thirdpartyflag 字段
 */

async function analyzeCheckboxVsMultiSelect() {
  console.log('🔍 分析 checkbox 和 multi_select 的区别...\n');

  try {
    // 1. 查看字段配置
    console.log('📋 步骤1: 查看字段配置');
    const fieldConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        fieldName: { in: ['gmpexemptflag', 'thirdpartyflag'] },
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        filterType: true,
        isFilterable: true,
      },
    });

    fieldConfigs.forEach(config => {
      console.log(`   ${config.fieldName}:`);
      console.log(`     显示名: ${config.displayName}`);
      console.log(`     字段类型: ${config.fieldType}`);
      console.log(`     筛选类型: ${config.filterType}`);
      console.log(`     可筛选: ${config.isFilterable}`);
      console.log('');
    });

    // 2. 查看实际数据
    console.log('📊 步骤2: 查看实际数据分布');
    
    // gmpexemptflag 数据分析
    console.log('   gmpexemptflag 数据分析:');
    const gmpData = await (db as any).uSClass.groupBy({
      by: ['gmpexemptflag'],
      _count: { gmpexemptflag: true },
      orderBy: { _count: { gmpexemptflag: 'desc' } },
    });

    gmpData.forEach((item: any) => {
      const value = item.gmpexemptflag;
      const count = item._count.gmpexemptflag;
      const displayValue = value === null ? 'NULL' : 
                          value === '' ? 'EMPTY' : 
                          value === true ? 'TRUE' : 
                          value === false ? 'FALSE' : 
                          String(value);
      console.log(`     ${displayValue}: ${count} 条记录`);
    });

    // thirdpartyflag 数据分析
    console.log('\n   thirdpartyflag 数据分析:');

    interface ThirdPartyGroupResult {
      thirdpartyflag: string | null;
      _count: {
        thirdpartyflag: number;
      };
    }

    const thirdPartyData = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } },
    }) as ThirdPartyGroupResult[];

    thirdPartyData.forEach((item) => {
      const value = item.thirdpartyflag;
      const count = item._count.thirdpartyflag;
      const displayValue = value === null ? 'NULL' :
                          value === '' ? 'EMPTY' :
                          String(value);
      console.log(`     ${displayValue}: ${count} 条记录`);
    });

    // 3. 查看数据库 schema 中的字段定义
    console.log('\n🏗️ 步骤3: 数据库字段类型分析');

    // 查看几条实际数据
    interface SampleDataResult {
      gmpexemptflag: string | null;
      thirdpartyflag: string | null;
    }

    const sampleData = await db.uSClass.findMany({
      select: {
        gmpexemptflag: true,
        thirdpartyflag: true,
      },
      take: 10,
    }) as SampleDataResult[];

    console.log('   样本数据:');
    sampleData.forEach((item: any, index: number) => {
      console.log(`     记录${index + 1}:`);
      console.log(`       gmpexemptflag: ${item.gmpexemptflag} (${typeof item.gmpexemptflag})`);
      console.log(`       thirdpartyflag: ${item.thirdpartyflag} (${typeof item.thirdpartyflag})`);
    });

    // 4. 分析前端渲染差异
    console.log('\n🎨 步骤4: 前端渲染差异分析');
    
    console.log('   checkbox 类型 (gmpexemptflag):');
    console.log('     - 使用 MultiSelect 组件');
    console.log('     - 布尔值显示为 Yes/No');
    console.log('     - label 转换: true → "Yes", false → "No"');
    console.log('     - 适用于布尔字段的多选筛选');

    console.log('\n   multi_select 类型 (thirdpartyflag):');
    console.log('     - 使用 MultiSelect 组件');
    console.log('     - 直接显示原始值');
    console.log('     - 适用于字符串字段的多选筛选');

    // 5. 后端处理差异
    console.log('\n⚙️ 步骤5: 后端处理差异分析');
    
    console.log('   checkbox 类型处理:');
    console.log('     - 字段类型通常是 boolean');
    console.log('     - 前端发送: ["true", "false"] 或 ["true"] 等');
    console.log('     - 后端转换: "true" → true, "false" → false');
    console.log('     - 空值处理: null, undefined');

    console.log('\n   multi_select 类型处理:');
    console.log('     - 字段类型通常是 text/varchar');
    console.log('     - 前端发送: ["value1", "value2"] 等');
    console.log('     - 后端使用: WHERE field IN (value1, value2)');
    console.log('     - 空值处理: null, "", "N/A"');

    // 6. 空值处理对比
    console.log('\n🔍 步骤6: 空值处理对比');
    
    // 检查空值情况
    const gmpNullCount = await (db as any).uSClass.count({
      where: { gmpexemptflag: null },
    });
    
    const thirdPartyNullCount = await (db as any).uSClass.count({
      where: { thirdpartyflag: null },
    });

    const thirdPartyEmptyCount = await (db as any).uSClass.count({
      where: { thirdpartyflag: '' },
    });

    console.log('   空值统计:');
    console.log(`     gmpexemptflag NULL: ${gmpNullCount} 条`);
    console.log(`     thirdpartyflag NULL: ${thirdPartyNullCount} 条`);
    console.log(`     thirdpartyflag EMPTY: ${thirdPartyEmptyCount} 条`);

    // 7. 总结差异
    console.log('\n📚 总结: checkbox vs multi_select 的关键差异');
    
    console.log('\n   🎯 设计用途:');
    console.log('     checkbox: 专为布尔字段设计，支持 Yes/No/未知 的多选筛选');
    console.log('     multi_select: 专为枚举字段设计，支持多个具体值的筛选');

    console.log('\n   🎨 前端显示:');
    console.log('     checkbox: true→"Yes", false→"No", null→"N/A"');
    console.log('     multi_select: 直接显示原始值');

    console.log('\n   ⚙️ 后端处理:');
    console.log('     checkbox: 布尔值转换 + 空值处理');
    console.log('     multi_select: 字符串数组 + N/A 处理');

    console.log('\n   🔍 空值处理:');
    console.log('     checkbox: 主要处理 null (未设置)');
    console.log('     multi_select: 处理 null, "", "N/A" (多种空值形式)');

    console.log('\n   👀 外观差异:');
    console.log('     两者都使用 MultiSelect 组件，外观基本相同');
    console.log('     主要差异在于选项的显示文本和数据处理逻辑');

  } catch (error) {
    console.error('\n❌ 分析失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行分析
analyzeCheckboxVsMultiSelect()
  .then(() => {
    console.log('\n✨ 分析完成!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 分析失败:', error);
    process.exit(1);
  });
