#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function checkCountryCodeData() {
  console.log('🔍 检查 us_pmn 数据库中 country_code 字段的实际数据...');
  
  try {
    // 检查 us_pmn 表中的 country_code 数据
    const countryCodeStats = await db.uSPremarketNotification.groupBy({
      by: ['country_code'],
      _count: { country_code: true },
      orderBy: { _count: { country_code: 'desc' } }
    });
    
    console.log('✅ us_pmn 中 country_code 的唯一值和数量:');
    countryCodeStats.forEach((item, index) => {
      console.log(`   ${index + 1}. "${item.country_code}" - ${item._count.country_code} 条记录`);
    });
    
    console.log(`\n📊 总共有 ${countryCodeStats.length} 个不同的 country_code 值`);
    
    // 检查总记录数
    const totalRecords = await db.uSPremarketNotification.count();
    console.log(`📊 us_pmn 总记录数: ${totalRecords}`);
    
  } catch (error) {
    console.error('❌ 检查 us_pmn 数据时出错:', error);
  }
  
  try {
    // 对比检查 us_class 表中的 deviceclass 数据
    console.log('\n🔍 检查 us_class 数据库中 deviceclass 字段的实际数据...');
    
    const deviceClassStats = await (db as any).uSClassification.groupBy({
      by: ['deviceclass'],
      _count: { deviceclass: true },
      orderBy: { _count: { deviceclass: 'desc' } }
    });
    
    console.log('✅ us_class 中 deviceclass 的唯一值和数量:');
    deviceClassStats.forEach((item: any, index: number) => {
      console.log(`   ${index + 1}. "${item.deviceclass}" - ${item._count.deviceclass} 条记录`);
    });
    
    console.log(`\n📊 总共有 ${deviceClassStats.length} 个不同的 deviceclass 值`);
    
    // 检查总记录数
    const totalClassRecords = await db.uSClassification.count();
    console.log(`📊 us_class 总记录数: ${totalClassRecords}`);
    
  } catch (error) {
    console.error('❌ 检查 us_class 数据时出错:', error);
  }
  
  await db.$disconnect();
}

checkCountryCodeData().catch(console.error);
