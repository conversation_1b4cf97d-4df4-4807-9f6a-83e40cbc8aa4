import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // TypeScript rules - stricter enforcement
      "@typescript-eslint/no-unused-vars": ["error", {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }],
      "@typescript-eslint/no-explicit-any": "error", // Enforce proper typing
      "@typescript-eslint/no-empty-object-type": "error", // Disallow empty interfaces
      "@typescript-eslint/prefer-as-const": "error",
      "@typescript-eslint/no-inferrable-types": "error",

      // React rules - stricter enforcement
      "react-hooks/exhaustive-deps": "error", // Enforce correct dependencies
      "react-hooks/rules-of-hooks": "error",
      "react/no-unescaped-entities": "error",

      // Next.js rules
      "@next/next/no-img-element": "warn",
      "@next/next/no-html-link-for-pages": "warn",

      // General code quality
      "prefer-const": "error",
      "no-var": "error",
      "no-console": ["warn", { "allow": ["warn", "error"] }],

      // Accessibility - keep some disabled for now but plan to enable
      "jsx-a11y/alt-text": "warn",
    },
  },
];

export default eslintConfig;
