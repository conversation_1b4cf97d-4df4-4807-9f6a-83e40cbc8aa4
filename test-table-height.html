<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Height Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 多行文本截断样式 */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
            max-height: calc(1.4em * 3);
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <h1 class="text-2xl font-bold mb-6">表格行高测试</h1>
    
    <div class="bg-white rounded-lg shadow">
        <h2 class="text-lg font-semibold p-4 border-b">修改前（单行显示）</h2>
        <div class="overflow-auto">
            <!-- 表头 -->
            <div class="flex bg-gray-50 border-b font-medium text-sm">
                <div class="px-4 py-2 w-32">K Number</div>
                <div class="px-4 py-2 w-64">Device Name</div>
                <div class="px-4 py-2 w-48">Applicant</div>
                <div class="px-4 py-2 w-32">Decision</div>
            </div>
            
            <!-- 数据行 - 原始样式 -->
            <div class="px-4 py-1.5 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors">
                <div class="flex text-sm min-w-max">
                    <div class="px-2 py-0 w-32">
                        <span class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer truncate block" title="K250972">K250972</span>
                    </div>
                    <div class="px-2 py-0 w-64">
                        <span class="text-gray-900 cursor-help truncate block" title="Primum Hydrophilic Guiding Catheter with Very Long Description That Should Be Truncated">Primum Hydrophilic Guiding Catheter with Very Long Description That Should Be Truncated</span>
                    </div>
                    <div class="px-2 py-0 w-48">
                        <span class="text-gray-900 cursor-help truncate block" title="Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)">Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)</span>
                    </div>
                    <div class="px-2 py-0 w-32">
                        <span class="text-gray-900 cursor-help truncate block" title="SESE">SESE</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow mt-8">
        <h2 class="text-lg font-semibold p-4 border-b">修改后（三行显示）</h2>
        <div class="overflow-auto">
            <!-- 表头 -->
            <div class="flex bg-gray-50 border-b font-medium text-sm">
                <div class="px-4 py-2 w-32">K Number</div>
                <div class="px-4 py-2 w-64">Device Name</div>
                <div class="px-4 py-2 w-48">Applicant</div>
                <div class="px-4 py-2 w-32">Decision</div>
            </div>
            
            <!-- 数据行 - 新样式 -->
            <div class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors">
                <div class="flex text-sm min-w-max">
                    <div class="px-2 py-1 w-32">
                        <span class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer block line-clamp-3" title="K250972">K250972</span>
                    </div>
                    <div class="px-2 py-1 w-64">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="Primum Hydrophilic Guiding Catheter with Very Long Description That Should Be Truncated and Show Multiple Lines">Primum Hydrophilic Guiding Catheter with Very Long Description That Should Be Truncated and Show Multiple Lines</span>
                    </div>
                    <div class="px-2 py-1 w-48">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)">Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)</span>
                    </div>
                    <div class="px-2 py-1 w-32">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="SESE">SESE</span>
                    </div>
                </div>
            </div>
            
            <!-- 另一行测试数据 -->
            <div class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors">
                <div class="flex text-sm min-w-max">
                    <div class="px-2 py-1 w-32">
                        <span class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer block line-clamp-3" title="K243077">K243077</span>
                    </div>
                    <div class="px-2 py-1 w-64">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="Affirm 800 Advanced Surgical Navigation System with Real-time Imaging and AI-powered Guidance Technology">Affirm 800 Advanced Surgical Navigation System with Real-time Imaging and AI-powered Guidance Technology</span>
                    </div>
                    <div class="px-2 py-1 w-48">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)">Digital Surgery Systems, Inc. (d.b.a True Digital Surgery)</span>
                    </div>
                    <div class="px-2 py-1 w-32">
                        <span class="text-gray-900 cursor-help block line-clamp-3" title="SESE">SESE</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="font-semibold text-blue-800 mb-2">测试说明：</h3>
        <ul class="text-blue-700 space-y-1">
            <li>• 行高从 py-1.5 (6px) 增加到 py-3 (12px)</li>
            <li>• 单元格内容从 py-0 增加到 py-1 (4px)</li>
            <li>• 移除 truncate 类，添加 line-clamp-3 类</li>
            <li>• 支持最多3行文本显示</li>
            <li>• 保持鼠标悬停显示完整内容的功能</li>
        </ul>
    </div>
</body>
</html>
