import { db } from '../lib/prisma';
import { AnalyticsQueries, analyticsCache } from '../lib/analytics-cache';

async function testAnalyticsPerformance() {
  console.error('🚀 开始Analytics性能测试...\n');

  // 清除缓存以获得准确的基准测试
  analyticsCache.clear();

  const timeRanges = ['1d', '7d', '30d'];
  const databases = ['deviceCNImported', 'deviceUS', null]; // null表示所有数据库

  console.error('📊 测试基础统计查询性能...');
  
  for (const timeRange of timeRanges) {
    for (const database of databases) {
      const dbLabel = database || 'all_databases';
      
      // 测试旧方法（直接查询）
      const oldStart = performance.now();
      await testOldMethod(timeRange, database);
      const oldTime = performance.now() - oldStart;
      
      // 测试新方法（缓存优化）
      const newStart = performance.now();
      await AnalyticsQueries.getCachedBasicStats(timeRange, database);
      const newTime = performance.now() - newStart;
      
      // 测试缓存命中
      const cacheStart = performance.now();
      await AnalyticsQueries.getCachedBasicStats(timeRange, database);
      const cacheTime = performance.now() - cacheStart;
      
      console.error(`  ${timeRange} - ${dbLabel}:`);
      console.error(`    旧方法: ${oldTime.toFixed(2)}ms`);
      console.error(`    新方法: ${newTime.toFixed(2)}ms (${((oldTime - newTime) / oldTime * 100).toFixed(1)}% 改进)`);
      console.error(`    缓存命中: ${cacheTime.toFixed(2)}ms (${((newTime - cacheTime) / newTime * 100).toFixed(1)}% 更快)`);
      console.error('');
    }
  }

  console.error('📈 测试跳出率计算性能...');
  
  for (const timeRange of timeRanges) {
    // 测试旧方法（内存中计算）
    const oldStart = performance.now();
    await testOldBounceRate(timeRange);
    const oldTime = performance.now() - oldStart;
    
    // 测试新方法（SQL优化）
    const newStart = performance.now();
    await AnalyticsQueries.getCachedBounceRate(timeRange);
    const newTime = performance.now() - newStart;
    
    console.error(`  ${timeRange}:`);
    console.error(`    旧方法: ${oldTime.toFixed(2)}ms`);
    console.error(`    新方法: ${newTime.toFixed(2)}ms (${((oldTime - newTime) / oldTime * 100).toFixed(1)}% 改进)`);
    console.error('');
  }

  console.error('🔍 测试数据库索引效果...');
  
  // 测试各种查询的性能
  const indexTests = [
    {
      name: '按时间范围查询',
      query: () => db.activityLog.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          }
        }
      })
    },
    {
      name: '按事件类型查询',
      query: () => db.activityLog.count({
        where: {
          eventType: 'pageview'
        }
      })
    },
    {
      name: '按数据库查询',
      query: () => db.activityLog.count({
        where: {
          database: 'deviceCNImported'
        }
      })
    },
    {
      name: '复合索引查询',
      query: () => db.activityLog.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
          eventType: 'database_search'
        }
      })
    }
  ];

  for (const test of indexTests) {
    const start = performance.now();
    const result = await test.query();
    const time = performance.now() - start;
    
    console.error(`  ${test.name}: ${time.toFixed(2)}ms (${result} 条记录)`);
  }

  console.error('\n📋 缓存统计信息:');
  const cacheStats = analyticsCache.getStats();
  console.error(`  缓存条目数: ${cacheStats.size}`);
  console.error(`  缓存键: ${cacheStats.keys.slice(0, 5).join(', ')}${cacheStats.keys.length > 5 ? '...' : ''}`);

  console.error('\n✅ 性能测试完成！');
}

// 旧的基础统计方法（用于对比）
async function testOldMethod(timeRange: string, database?: string) {
  const now = new Date();
  let startDate: Date;
  
  switch (timeRange) {
    case '1d':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }

  const whereCondition: Record<string, unknown> = {
    createdAt: {
      gte: startDate,
      lte: now,
    },
  };

  if (database) {
    whereCondition.database = database;
  }

  // 模拟旧的并行查询方式
  const [totalVisits, uniqueVisitors, pageViews, searchEvents] = await Promise.all([
    db.activityLog.count({ where: whereCondition }),
    db.activityLog.findMany({
      where: whereCondition,
      select: { ip: true },
      distinct: ['ip'],
    }),
    db.activityLog.count({
      where: { ...whereCondition, eventType: 'pageview' }
    }),
    db.activityLog.count({
      where: {
        ...whereCondition,
        eventType: { in: ['database_search', 'advanced_search'] }
      }
    }),
  ]);

  return {
    totalVisits,
    uniqueVisitors: uniqueVisitors.length,
    pageViews,
    searchEvents,
  };
}

// 旧的跳出率计算方法（用于对比）
async function testOldBounceRate(timeRange: string) {
  const now = new Date();
  const startDate = getStartDate(timeRange, now);

  const sessions = await db.activityLog.findMany({
    where: {
      createdAt: { gte: startDate, lte: now },
      sessionId: { not: null },
    },
    select: { sessionId: true },
  });

  const sessionMap: Record<string, number> = {};
  sessions.forEach(({ sessionId }) => {
    if (sessionId) {
      sessionMap[sessionId] = (sessionMap[sessionId] || 0) + 1;
    }
  });

  const totalSessions = Object.keys(sessionMap).length;
  const bouncedSessions = Object.values(sessionMap).filter(count => count === 1).length;
  
  return totalSessions > 0 ? (bouncedSessions / totalSessions) * 100 : 0;
}

function getStartDate(timeRange: string, now: Date): Date {
  switch (timeRange) {
    case '1d':
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAnalyticsPerformance()
    .catch(error => {
      console.error('性能测试失败:', error);
      process.exit(1);
    })
    .finally(() => {
      process.exit(0);
    });
}

export { testAnalyticsPerformance };
