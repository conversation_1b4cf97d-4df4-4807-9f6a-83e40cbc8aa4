import { db } from '../lib/prisma';
import { getTableConfig, getSupportedTables } from '../lib/uniqueKeyConfig';

// 数据管理工具类
export class DataManager {
  
  // 查看表统计信息
  async getTableStats(tableName: string) {
    try {
      const totalCount = await db.medicalDevice.count();
      const activeCount = await db.medicalDevice.count({ where: { isActive: true } });
      const inactiveCount = await db.medicalDevice.count({ where: { isActive: false } });
      
      console.error(`\n📊 ${tableName} 表统计信息:`);
      console.error(`  总记录数: ${totalCount}`);
      console.error(`  激活记录: ${activeCount}`);
      console.error(`  非激活记录: ${inactiveCount}`);
      
      // 按数据库类型统计
      const databaseStats = await db.medicalDevice.groupBy({
        by: ['database'],
        _count: { database: true },
        where: { isActive: true }
      });
      
      console.error('\n📋 按数据库类型统计:');
      databaseStats.forEach(stat => {
        console.error(`  ${stat.database}: ${stat._count.database} 条记录`);
      });
      
    } catch (__error) {
      console.error('获取表统计信息失败:', error);
    }
  }

  // 查看变更历史
  async getChangeHistory(businessKey?: string, limit = 0) {
    try {
      const where = businessKey ? { businessKey } : {};
      const logs = await db.dataChangeLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      console.error(`\n📝 变更历史 (${logs.length} 条):`);
      logs.forEach((log, _index) => {
        console.error(`\n  ${index + 1}. ${log.operation} - ${log.businessKey}`);
        console.error(`     时间: ${log.createdAt.toLocaleString()}`);
        console.error(`     操作人: ${log.importedBy || '未知'}`);
        console.error(`     来源: ${log.importedFrom || '未知'}`);
        if (log.changeReason) {
          console.error(`     原因: ${log.changeReason}`);
        }
      });

    } catch (__error) {
      console.error('获取变更历史失败:', error);
    }
  }

  // 清理非激活数据
  async cleanupInactiveData(tableName: string, days = 0) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const inactiveRecords = await db.medicalDevice.findMany({
        where: {
          isActive: false,
          updatedAt: { lt: cutoffDate }
        }
      });

      console.error(`\n🗑️ 清理非激活数据:`);
      console.error(`  清理条件: 非激活且超过 ${days} 天未更新`);
      console.error(`  找到 ${inactiveRecords.length} 条记录`);

      if (inactiveRecords.length === 0) {
        console.error('  没有需要清理的数据');
        return;
      }

      // 这里可以选择物理删除或标记删除
      // 为了安全，这里只显示统计信息
      console.error('  注意: 为了数据安全，请手动确认是否删除这些记录');
      console.error('  建议: 先备份数据，再执行删除操作');

    } catch (__error) {
      console.error('清理非激活数据失败:', error);
    }
  }

  // 验证数据完整性
  async validateDataIntegrity(tableName: string) {
    try {
      console.error(`\n🔍 验证 ${tableName} 数据完整性:`);

      // 检查重复的businessKey
      const duplicates = await db.$queryRaw`
        SELECT "businessKey", COUNT(*) as count
        FROM "MedicalDevice"
        GROUP BY "businessKey"
        HAVING COUNT(*) > 1
        ORDER BY count DESC
      `;

      if (Array.isArray(duplicates) && duplicates.length > 0) {
        console.error(`  ❌ 发现 ${duplicates.length} 个重复的businessKey:`);
        duplicates.forEach((dup: Record<string, unknown>) => {
          console.error(`    ${dup.businessKey}: ${dup.count} 条记录`);
        });
      } else {
        console.error('  ✅ 没有发现重复的businessKey');
      }

      // 检查必填字段
      const missingRequired = await db.medicalDevice.findMany({
        where: {
          OR: [
            { productName: null },
            { productName: '' },
            { companyName: null },
            { companyName: '' },
            { database: null },
            { database: '' }
          ]
        }
      });

      if (missingRequired.length > 0) {
        console.error(`  ⚠️ 发现 ${missingRequired.length} 条记录缺少必填字段`);
      } else {
        console.error('  ✅ 所有记录都包含必填字段');
      }

      // 检查数据版本
      const versionStats = await db.medicalDevice.groupBy({
        by: ['dataVersion'],
        _count: { dataVersion: true }
      });

      console.error('\n📋 数据版本统计:');
      versionStats.forEach(stat => {
        console.error(`  版本 ${stat.dataVersion}: ${stat._count.dataVersion} 条记录`);
      });

    } catch (__error) {
      console.error('验证数据完整性失败:', error);
    }
  }

  // 显示支持的表
  showSupportedTables() {
    const tables = getSupportedTables();
    console.error('\n📋 支持的表:');
    tables.forEach(tableName => {
      const config = getTableConfig(tableName);
      console.error(`  ${tableName}: ${config?.description || '无描述'}`);
    });
  }

  // 显示表配置
  showTableConfig(tableName: string) {
    const config = getTableConfig(tableName);
    if (!config) {
      console.error(`❌ 表 ${tableName} 不存在配置`);
      return;
    }

    console.error(`\n⚙️ ${tableName} 表配置:`);
    console.error(`  描述: ${config.description || '无'}`);
    console.error(`  导入模式: ${config.importMode}`);
    
    if (config.validationRules) {
      console.error(`  必填字段: ${config.validationRules.requiredFields?.join(', ') || '无'}`);
      console.error(`  唯一字段: ${config.validationRules.uniqueFields?.join(', ') || '无'}`);
    }

    if (config.fieldMapping) {
      console.error(`  字段映射: ${Object.keys(config.fieldMapping).length} 个映射`);
    }
  }
}

// 命令行工具
async function main() {
  const command = process.argv[2];
  const tableName = process.argv[3] || 'MedicalDevice';
  const dataManager = new DataManager();

  switch (command) {
    case 'stats':
      await dataManager.getTableStats(tableName);
      break;
      
    case 'history':
      const businessKey = process.argv[4];
      const limit = parseInt(process.argv[5]) || 20;
      await dataManager.getChangeHistory(businessKey, limit);
      break;
      
    case 'cleanup':
      const days = parseInt(process.argv[4]) || 30;
      await dataManager.cleanupInactiveData(tableName, days);
      break;
      
    case 'validate':
      await dataManager.validateDataIntegrity(tableName);
      break;
      
    case 'tables':
      dataManager.showSupportedTables();
      break;
      
    case 'config':
      dataManager.showTableConfig(tableName);
      break;
      
    default:
      console.error('数据管理工具使用说明:');
      console.error('  npm run data-manager stats [表名]                    - 查看表统计信息');
      console.error('  npm run data-manager history [业务键] [数量]        - 查看变更历史');
      console.error('  npm run data-manager cleanup [天数]                 - 清理非激活数据');
      console.error('  npm run data-manager validate [表名]                - 验证数据完整性');
      console.error('  npm run data-manager tables                         - 显示支持的表');
      console.error('  npm run data-manager config [表名]                  - 显示表配置');
      break;
  }

  await db.$disconnect();
}

main(); 