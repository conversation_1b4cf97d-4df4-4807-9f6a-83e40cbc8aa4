'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Database,
  Activity,
  TrendingDown,
  Percent,
  BarChart3,
  Building2,
  Users,
  Calendar,
  Calculator,
  TrendingUp,
  MinusCircle,
  PlusCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface StatisticItem {
  name: string;
  count?: number;
  value?: number;
}

interface StatisticData {
  type: 'group_by' | 'sum' | 'avg' | 'min_max';
  items?: StatisticItem[];
  total?: number;
  count?: number;
  average?: number;
  min?: Record<string, unknown>;
  max?: Record<string, unknown>;
}

interface StatisticField {
  fieldName: string;
  displayName: string;
  statisticsType: string;
  data: StatisticData;
  order: number;
}

interface StatsData {
  basic: {
    total: number;
    active: number;
    inactive: number;
  };
  statistics: StatisticField[];
}

interface ConfigurableStatsPanelProps {
  database: string;
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
  maxHeight?: number;
  filters?: Record<string, unknown>;
}

export default function ConfigurableStatsPanel({
  database,
  isOpen,
  onToggle,
  className = "",
  maxHeight = 400,
  filters = {}
}: ConfigurableStatsPanelProps) {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const toggleCardExpansion = (fieldName: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(fieldName)) {
      newExpanded.delete(fieldName);
    } else {
      newExpanded.add(fieldName);
    }
    setExpandedCards(newExpanded);
  };

  useEffect(() => {
    if (isOpen) {
      const fetchStats = async () => {
        try {
          setLoading(true);
          setError(null);
          const params = new URLSearchParams();
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== "") {
              if (Array.isArray(value)) {
                value.forEach(v => params.append(key, String(v)));
              } else {
                params.append(key, String(value));
              }
            }
          });
          const url = `/api/stats/${database}/configurable` + (params.toString() ? `?${params.toString()}` : "");
          const response = await fetch(url);
          const result = await response.json();
          
          if (result.success) {
            setStats(result.data);
          } else {
            setError(result.error || 'Failed to fetch statistics');
          }
        } catch (__err) {
          setError('Network error, please try again later');
          console.error('Failed to fetch configurable stats:', err);
        } finally {
          setLoading(false);
        }
      };

      fetchStats();
    }
  }, [database, isOpen, filters]);

  const getIconForStatType = (type: string) => {
    switch (type) {
      case 'group_by':
      case 'count':
        return <BarChart3 className="h-5 w-5" />;
      case 'sum':
        return <Calculator className="h-5 w-5" />;
      case 'avg':
        return <TrendingUp className="h-5 w-5" />;
      case 'min_max':
        return <MinusCircle className="h-5 w-5" />;
      default:
        return <Activity className="h-5 w-5" />;
    }
  };

  const renderStatisticCard = (statField: StatisticField) => {
    const { displayName, data, statisticsType, fieldName } = statField;
    const isExpanded = expandedCards.has(fieldName);

    // 从配置中获取显示数量
    const defaultLimit = (statField as any).statisticsDefaultLimit || 5;
    const maxLimit = (statField as any).statisticsMaxLimit || 50;
    const totalItems = data.items?.length || 0;
    const displayLimit = isExpanded ? Math.min(maxLimit, totalItems) : Math.min(defaultLimit, totalItems);

    return (
      <Card key={statField.fieldName}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getIconForStatType(statisticsType)}
              {displayName}
            </div>
            {data.type ==="group_by" && data.items && totalItems > defaultLimit && (
              <button
                onClick={() => toggleCardExpansion(fieldName)}
                className="text-xs text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-3 w-3" />
                    Collapse
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-3 w-3" />
                    Expand ({totalItems})
                  </>
                )}
              </button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.type ==="group_by" && data.items && (
            <div
              className={`space-y-2 ${
                isExpanded && totalItems > 8
                  ? 'max-h-64 overflow-y-auto custom-scrollbar pr-2'
                  : ''
              }`}
            >
              {data.items.slice(0, displayLimit).map((item, _index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm truncate flex-1" title={item.name}>
                    {item.name}
                  </span>
                  <Badge variant="secondary" className="ml-2 flex-shrink-0">
                    {item.count?.toLocaleString()}
                  </Badge>
                </div>
              ))}
              {!isExpanded && totalItems > defaultLimit && (
                <div className="text-xs text-muted-foreground text-center pt-2 border-t">
                  {totalItems - defaultLimit} more items...
                </div>
              )}
            </div>
          )}
          
          {data.type ==="sum" && (
            <div>
              <div className="text-2xl font-bold">{data.total?.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                基于 {data.count?.toLocaleString()} 条记录
              </p>
            </div>
          )}
          
          {data.type ==="avg" && (
            <div>
              <div className="text-2xl font-bold">{data.average?.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">
                平均值 ({data.count?.toLocaleString()} 条记录)
              </p>
            </div>
          )}
          
          {data.type ==="min_max" && (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">最小值:</span>
                <span className="font-medium">{data.min}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">最大值:</span>
                <span className="font-medium">{data.max}</span>
              </div>
              <p className="text-xs text-muted-foreground">
                基于 {data.count?.toLocaleString()} 条记录
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`transition-all duration-300 ease-in-out ${className}`}>
      <div 
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${isOpen ? 'opacity-100' : 'opacity-0'}
        `}
        style={{ maxHeight: isOpen ? maxHeight : 0 }}
      >
        <div className="space-y-6 pt-4 overflow-y-auto custom-scrollbar" style={{ maxHeight: maxHeight }}>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="pb-2">
                    <Skeleton className="h-4 w-20" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-3 w-24" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error || !stats ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-red-500">{error || '无法加载统计数据'}</p>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* 基础统计 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">总数据量</CardTitle>
                    <Database className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.basic.total.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      活跃: {stats.basic.active.toLocaleString()}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">非活跃</CardTitle>
                    <TrendingDown className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.basic.inactive.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      非活跃记录
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">活跃率</CardTitle>
                    <Percent className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.basic.total > 0 ? Math.round((stats.basic.active / stats.basic.total) * 100) : 0}%
                    </div>
                    <p className="text-xs text-muted-foreground">
                      活跃记录占比
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">统计项目</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.statistics.length}</div>
                    <p className="text-xs text-muted-foreground">
                      配置的统计项
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* 配置的统计项 */}
              {stats.statistics.length > 0 && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {stats.statistics.map(renderStatisticCard)}
                </div>
              )}

              {stats.statistics.length === 0 && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-muted-foreground">暂无配置的统计项目</p>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
