"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Lock, Crown, Building, Check, ArrowRight } from "lucide-react";
import { MEMBERSHIP_BENEFITS, DATABASE_CONFIGS, type MembershipType } from "@/lib/permissions";
import { useAuth } from "@/lib/auth";

interface AccessRestrictedAlertProps {
  databaseCode: string;
  requiredLevel: MembershipType;
}

export default function AccessRestrictedAlert({ databaseCode, requiredLevel }: AccessRestrictedAlertProps) {
  const { user } = useAuth();
  const databaseConfig = DATABASE_CONFIGS[databaseCode as keyof typeof DATABASE_CONFIGS];
  const requiredBenefits = MEMBERSHIP_BENEFITS[requiredLevel];

  if (!databaseConfig) {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Main Alert */}
      <Alert className="mb-8 border-yellow-200 bg-yellow-50">
        <Lock className="h-5 w-5 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Access Restricted</strong> - "{databaseConfig.name}" database requires {requiredBenefits.name} privileges
            </div>
            {user ? (
              <Button asChild size="sm">
                <Link href="/upgrade">Upgrade Now</Link>
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/login">Login</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/register">Register</Link>
                </Button>
              </div>
            )}
          </div>
        </AlertDescription>
      </Alert>

      {/* Database Info */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <span className="text-2xl mr-2">{databaseConfig.icon}</span>
            {databaseConfig.name}
            <Badge variant="secondary" className={`ml-2 ${
              requiredLevel ==="premium" ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
            }`}>
              {requiredLevel ==="premium" && <Crown className="h-3 w-3 mr-1" />}
              {requiredLevel ==="enterprise" && <Building className="h-3 w-3 mr-1" />}
              {requiredBenefits.name} Exclusive
            </Badge>
          </CardTitle>
          <CardDescription className="text-base">
            {databaseConfig.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            This database contains high-value commercial data and requires {requiredBenefits.name} privileges to access complete content.
          </p>
        </CardContent>
      </Card>

      {/* Membership Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Plan */}
        {user && (
          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="flex items-center text-gray-600">
                Current Plan: {MEMBERSHIP_BENEFITS[user.membershipType].name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {MEMBERSHIP_BENEFITS[user.membershipType].features.slice(0, 3).map((feature) => (
                  <li key={feature} className="flex items-start text-sm">
                    <Check className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Required Plan */}
        <Card className={`border-2 ${
          requiredLevel ==="premium" ? 'border-blue-300 bg-blue-50' : 'border-purple-300 bg-purple-50'
        }`}>
          <CardHeader>
            <CardTitle className={`flex items-center ${
              requiredLevel ==="premium" ? 'text-blue-600' : 'text-purple-600'
            }`}>
              {requiredLevel ==="premium" && <Crown className="h-5 w-5 mr-2" />}
              {requiredLevel ==="enterprise" && <Building className="h-5 w-5 mr-2" />}
              {requiredBenefits.name}
              <Badge className={`ml-2 ${
                requiredLevel ==="premium" ? 'bg-blue-600' : 'bg-purple-600'
              }`}>
                Recommended
              </Badge>
            </CardTitle>
            <CardDescription>{requiredBenefits.description}</CardDescription>
            {'price' in requiredBenefits && requiredBenefits.price && (
              <div className="text-sm">
                <span className="text-2xl font-bold">¥{requiredBenefits.price.monthly}</span>
                <span className="text-gray-500">/month</span>
                <span className="ml-2 text-gray-500">
                  Annual ¥{requiredBenefits.price.yearly}
                </span>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 mb-4">
              {requiredBenefits.features.map((feature) => (
                <li key={feature} className="flex items-start text-sm">
                  <Check className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>

            <Button
              className={`w-full ${
                requiredLevel ==="premium" ? 'bg-blue-600 hover:bg-blue-700' : 'bg-purple-600 hover:bg-purple-700'
              }`}
              asChild
            >
              <Link href={`/upgrade?plan=${requiredLevel}`}>
                Upgrade to {requiredBenefits.name}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Benefits Highlight */}
      <Card className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Upgrade Now, Unlock All Features
            </h3>
            <p className="text-gray-600 mb-4">
              Get complete access to medical device data and improve your R&D and business decision efficiency
            </p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/demo">View Demo</Link>
              </Button>
              <Button asChild>
                <Link href="/upgrade">Start Upgrade</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
