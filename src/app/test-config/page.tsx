'use client';

import { useState, useEffect } from 'react';
import { getDatabaseConfigs } from '@/lib/permissions';

interface DatabaseConfig {
  name: string;
  category: string;
  description: string;
  accessLevel: string;
  icon?: string;
  defaultSort?: unknown;
}

export default function TestConfigPage() {
  const [configs, setConfigs] = useState<Record<string, DatabaseConfig>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        console.error('🔍 Starting to fetch database configurations...');

        // Call API directly instead of through getDatabaseConfigs
        const response = await fetch('/api/config/databases');
        console.error('📡 API response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.error('📋 API response data:', result);

        if (!result.success) {
          throw new Error(result.error || 'API returned failure');
        }

        console.error('✅ Configuration fetch successful:', result.data);
        setConfigs(result.data);
      } catch (__err) {
        console.error('❌ Configuration fetch failed:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchConfigs();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Configuration Test Page</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Configuration Test Page</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Configuration Test Page</h1>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Database Configurations</h2>
        <p className="text-gray-600">Found {Object.keys(configs).length} database configurations</p>
      </div>

      <div className="space-y-4">
        {Object.entries(configs).map(([code, config]) => (
          <div key={code} className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold text-lg">{config.name}</h3>
            <div className="mt-2 space-y-1 text-sm">
              <p><strong>Code:</strong> {code}</p>
              <p><strong>Category:</strong> {config.category}</p>
              <p><strong>Description:</strong> {config.description}</p>
              <p><strong>Access Level:</strong> {config.accessLevel}</p>
              <p><strong>Icon:</strong> {config.icon}</p>
              {config.defaultSort && (
                <p><strong>Default Sort:</strong> {JSON.stringify(config.defaultSort)}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
