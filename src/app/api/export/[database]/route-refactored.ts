import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode, getDatabaseAccessLevel } from '@/lib/dynamicTableMapping';
import { checkPermissions } from '@/lib/server/permissions';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildMedicalDeviceWhere } from '@/lib/server/buildMedicalDeviceWhere';
import * as XLSX from 'xlsx';
import { formatExportDate } from '@/lib/utils';

export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    console.error('Export request for database:', database);

    // 使用新的统一验证函数
    const validationError = validateDatabaseCode(database);
    if (validationError) {
      return NextResponse.json(
        { success: false, error: validationError.error },
        { status: validationError.status }
      );
    }

    // 权限检查
    const requiredLevel = getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions, please upgrade membership' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv'; // csv, excel
    const limit = Number.parseInt(searchParams.get('limit') || '5000'); // 默认导出限制

    console.error('Export format:', format, 'limit:', limit);

    // 获取配置
    let config;
    try {
      config = await getDatabaseConfig(database);
      console.error('Config loaded:', !!config);
    } catch (_configError) {
      console.error('Config loading error:', configError);
      return NextResponse.json(
        { success: false, error: 'Failed to load database configuration' },
        { status: 500 }
      );
    }

    if (!config) {
      return NextResponse.json(
        { success: false, error: 'Database configuration not found' },
        { status: 404 }
      );
    }

    // 使用动态模型获取
    const model = getDynamicModel(database);
    if (!isPrismaModel(model)) {
      console.error('Model not found for:', database);
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    // 构建查询条件 - 重构版本：不再需要database字段过滤
    const where = buildMedicalDeviceWhere(searchParams, config);
    console.error('Query where condition:', Object.keys(where));

    // 获取可见字段
    let visibleFields;
    try {
      visibleFields = config.fields
        ?.filter(f => f.isVisible)
        ?.sort((a, b) => a.listOrder - b.listOrder) || [];
      console.error('Visible fields count:', visibleFields.length);
    } catch (_fieldsError) {
      console.error('Fields processing error:', fieldsError);
      // 使用基本字段作为后备
      visibleFields = [
        { fieldName: 'productName', displayName: 'Product Name', fieldType: 'text' },
        { fieldName: 'companyName', displayName: 'Company Name', fieldType: 'text' },
        { fieldName: 'approvalDate', displayName: 'Approval Date', fieldType: 'date' },
      ];
    }

    // 查询数据
    let data;
    try {
      // 构建select对象
      const select: Record<string, boolean> = {};
      visibleFields.forEach(field => {
        select[field.fieldName] = true;
      });
      select['id'] = true; // 主键始终包含，但不再包含database字段

      console.error('Querying data with select fields:', Object.keys(select));

      data = await (model as any).findMany({
        where,
        select,
        take: limit,
        orderBy: { createdAt: 'desc' },
      });

      console.error('Data queried successfully, count:', data.length);
    } catch (_queryError) {
      console.error('Data query error:', queryError);
      return NextResponse.json(
        { success: false, error: 'Failed to query data' },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No data found for export' },
        { status: 404 }
      );
    }

    // 准备导出数据
    const exportData = data.map((item: Record<string, unknown>) => {
      const exportItem: Record<string, any> = {};
      visibleFields.forEach(field => {
        const value = item[field.fieldName];
        // 格式化数据
        if (field.fieldType === 'date' && value) {
          exportItem[field.displayName] = formatExportDate(value);
        } else if (field.fieldType === 'boolean' && value !== undefined) {
          exportItem[field.displayName] = value ? 'Yes' : 'No';
        } else {
          exportItem[field.displayName] = value || '';
        }
      });
      return exportItem;
    });

    // 生成文件
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `${database}_export_${timestamp}`;

    if (format === 'excel') {
      // Excel导出
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${filename}.xlsx"`,
        },
      });
    } else {
      // CSV导出
      const headers = visibleFields.map(f => f.displayName);
      const csvContent = [
        headers.join(','),
                 ...exportData.map((row: Record<string, any>) => 
           headers.map(header => {
             const value = row[header] || '';
             // 处理包含逗号的值
             return typeof value === 'string' && value.includes(',') 
               ? `"${value.replace(/"/g, '""')}"` 
               : value;
           }).join(',')
         )
      ].join('\n');

      // 添加UTF-8 BOM以确保中文正确显示
      const bom = '\ufeff';
      const csvWithBom = bom + csvContent;

      return new NextResponse(csvWithBom, {
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="${filename}.csv"`,
        },
      });
    }

  } catch (__error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { success: false, error: 'Export failed' },
      { status: 500 }
    );
  }
} 