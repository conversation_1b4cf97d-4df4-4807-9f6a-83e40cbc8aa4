import { NextRequest, NextResponse } from 'next/server';
import { StaticTableMappingService } from '@/lib/staticTableMappingService';

/**
 * 配置刷新API
 * POST /api/admin/refresh-config - 刷新配置缓存
 * GET /api/admin/refresh-config - 查看配置状态
 */

export async function POST(__request: NextRequest) {
  try {
    console.error('🔄 Starting configuration cache refresh...');
    
    // 强制重新初始化配置服务
    await StaticTableMappingService.refresh();
    
    // 获取最新的配置统计
    const stats = StaticTableMappingService.getInitializationStatus();
    
    console.error('✅ Configuration cache refresh completed');
    
    return NextResponse.json({
      success: true,
      message: 'Configuration cache successfully refreshed',
      timestamp: new Date().toISOString(),
      stats: {
        initialized: stats.initialized,
        configCount: stats.configCount,
        configCodes: stats.configCodes
      }
    });
    
  } catch (__error) {
    console.error('❌ Configuration refresh failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Configuration refresh failed',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(__request: NextRequest) {
  try {
    // 获取当前配置状态
    const stats = StaticTableMappingService.getInitializationStatus();
    
    return NextResponse.json({
      success: true,
      message: '配置状态查询成功',
      timestamp: new Date().toISOString(),
      stats: {
        initialized: stats.initialized,
        configCount: stats.configCount,
        configCodes: stats.configCodes,
        availableDatabases: stats.configCodes
      }
    });
    
  } catch (__error) {
    console.error('❌ 配置状态查询失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '配置状态查询失败',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 