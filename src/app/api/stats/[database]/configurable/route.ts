import { NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildMedicalDeviceWhere } from '@/lib/server/buildMedicalDeviceWhere';

/**
 * 可配置的统计API
 * 根据fieldConfig中的统计配置动态生成统计数据
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const resolvedParams = await params;
    const database = resolvedParams.database;

    // 获取动态模型
    const model = await getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    const { searchParams } = new URL(request.url);
    
    // 构建where条件
    const where = buildMedicalDeviceWhere(searchParams, config);

    // 获取启用统计的字段配置
    const statisticsFields = config.fields
      .filter(field => field.isStatisticsEnabled)
      .sort((a, b) => (a.statisticsOrder || 0) - (b.statisticsOrder || 0));

    if (statisticsFields.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          basic: { total: 0, active: 0, inactive: 0 },
          statistics: [],
        },
        message: 'No statistics fields configured'
      });
    }

    // 基础统计
    const totalCount = await (model as any).count({ where });
    let activeCount = 0;
    try {
      activeCount = await (model as any).count({ 
        where: { ...where, isActive: true } 
      });
    } catch (__error) {
      activeCount = totalCount; // 如果没有isActive字段，回退到总数
    }

    // 动态生成统计数据
    const statistics = [];
    
    for (const fieldConfig of statisticsFields) {
      const fieldName = fieldConfig.fieldName;
      const displayName = fieldConfig.statisticsDisplayName || fieldConfig.displayName;
      const statisticsType = fieldConfig.statisticsType || 'count';
      const config = fieldConfig.statisticsConfig || {};
      
      try {
        let statisticData: Record<string, unknown> = null;

        switch (statisticsType) {
          case 'count':
          case 'group_by':
            // 分组计数统计 - 增加默认限制以支持展开功能
            const limit = (config as any).limit || 50; // 增加到50以支持更多数据展示
            // 获取排序方向配置，默认为倒序
            const sortOrder = (fieldConfig as any).statisticsSortOrder || 'desc';

            const groupByResults = await (model as any).groupBy({
              by: [fieldName],
              where: {
                ...where,
                [fieldName]: { not: null },
                ...(activeCount < totalCount ? { isActive: true } : {})
              },
              _count: { [fieldName]: true },
              orderBy: { _count: { [fieldName]: sortOrder } },
              take: limit,
            });

            statisticData = {
              type: 'group_by',
              items: groupByResults.map((item: Record<string, unknown>) => ({
                name: item[fieldName],
                count: item._count[fieldName],
              }))
            };
            break;

          case 'sum':
            // 求和统计（仅适用于数值字段）
            if (fieldConfig.fieldType === 'number') {
              const sumResult = await (model as any).aggregate({
                where: { 
                  ...where, 
                  [fieldName]: { not: null },
                  ...(activeCount < totalCount ? { isActive: true } : {})
                },
                _sum: { [fieldName]: true },
                _count: { [fieldName]: true },
              });
              
              statisticData = {
                type: 'sum',
                total: sumResult._sum[fieldName] || 0,
                count: sumResult._count[fieldName] || 0,
              };
            }
            break;

          case 'avg':
            // 平均值统计（仅适用于数值字段）
            if (fieldConfig.fieldType === 'number') {
              const avgResult = await (model as any).aggregate({
                where: { 
                  ...where, 
                  [fieldName]: { not: null },
                  ...(activeCount < totalCount ? { isActive: true } : {})
                },
                _avg: { [fieldName]: true },
                _count: { [fieldName]: true },
              });
              
              statisticData = {
                type: 'avg',
                average: avgResult._avg[fieldName] || 0,
                count: avgResult._count[fieldName] || 0,
              };
            }
            break;

          case 'min_max':
            // 最值统计（适用于数值和日期字段）
            if (fieldConfig.fieldType === 'number' || fieldConfig.fieldType === 'date') {
              const minMaxResult = await (model as any).aggregate({
                where: { 
                  ...where, 
                  [fieldName]: { not: null },
                  ...(activeCount < totalCount ? { isActive: true } : {})
                },
                _min: { [fieldName]: true },
                _max: { [fieldName]: true },
                _count: { [fieldName]: true },
              });
              
              statisticData = {
                type: 'min_max',
                min: minMaxResult._min[fieldName],
                max: minMaxResult._max[fieldName],
                count: minMaxResult._count[fieldName] || 0,
              };
            }
            break;
        }

        if (statisticData) {
          statistics.push({
            fieldName,
            displayName,
            statisticsType,
            statisticsDefaultLimit: fieldConfig.statisticsDefaultLimit || 5,
            statisticsMaxLimit: fieldConfig.statisticsMaxLimit || 50,
            data: statisticData,
            order: fieldConfig.statisticsOrder || 0,
          });
        }

      } catch (__error) {
        console.error(`统计字段 ${fieldName} 处理失败:`, error);
        // 继续处理其他字段，不中断整个统计过程
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        basic: {
          total: totalCount,
          active: activeCount,
          inactive: totalCount - activeCount,
        },
        statistics,
      },
      appliedFilters: Object.fromEntries(searchParams.entries()),
      databaseInfo: {
        code: database,
        totalFields: config.fields.length,
        statisticsFields: statisticsFields.length,
      }
    });

  } catch (__error) {
    console.error('Configurable Statistics API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
