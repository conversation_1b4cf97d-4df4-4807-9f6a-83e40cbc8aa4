import { NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 使用新的统一验证函数
    const validationError = validateDatabaseCode(database);
    if (validationError) {
      return NextResponse.json(
        { success: false, error: validationError.error },
        { status: validationError.status }
      );
    }

    // 使用动态模型获取
    const model = getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置（带回退）
    const config: DatabaseConfig = await getDatabaseConfig(database);
    // 只返回配置表中isFilterable的字段
    const metaFields = config.fields.filter(f => f.isFilterable);
    const metadata: Record<string, string[]> = {};
    const metadataWithCounts: Record<string, Array<{ value: string; count: number }>> = {};

    // 获取元数据，重构版本：不再需要database字段过滤
    for (const field of metaFields) {
      const fieldName = field.fieldName;
      
      try {
        // 分别获取非空值和空值的计数
        // 1. 获取非空值的计数
        const groupResults = await (model as any).groupBy({
          by: [fieldName],
          _count: { [fieldName]: true },
          where: {
            [fieldName]: { not: null, not: '' }, // 排除null和空字符串
            isActive: true, // 只获取活跃记录的元数据
          },
          orderBy: { _count: { [fieldName]: 'desc' } },
          take: 100, // 限制返回数量，避免内存问题
        });

        // 2. 单独统计空值（null和空字符串）
        const nullCount = await (model as any).count({
          where: {
            isActive: true,
            OR: [
              { [fieldName]: null },
              { [fieldName]: '' }
            ]
          }
        });

        // 3. 合并非空值和空值的结果
        const nonNullValues = groupResults.map((item: Record<string, any>) => ({
          value: String(item[fieldName]).trim(),
          count: item._count[fieldName] || 0,
          isNull: false
        }));

        // 4. 添加N/A项（如果有空值）
        const valuesWithCounts = [...nonNullValues];
        if (nullCount > 0) {
          valuesWithCounts.push({
            value: 'N/A',
            count: nullCount,
            isNull: true
          });
        }

        // 5. 排序：按计数倒序，N/A项排在最后
        valuesWithCounts.sort((a, b) => {
          if (a.isNull && !b.isNull) return 1;
          if (!a.isNull && b.isNull) return -1;
          return b.count - a.count; // 按计数倒序排列
        });

        // 6. 转换为最终格式
        const finalValuesWithCounts = valuesWithCounts.map(item => ({
          value: item.value,
          count: item.count
        }));

        const values = finalValuesWithCounts.map(item => item.value);

        metadata[fieldName] = values;
        metadataWithCounts[fieldName] = finalValuesWithCounts;

      } catch (_fieldError) {
        console.error(`获取字段 ${fieldName} 元数据失败:`, fieldError);
        // 对于出错的字段，返回空数组
        metadata[fieldName] = [];
        metadataWithCounts[fieldName] = [];
      }
    }

    return NextResponse.json({
      success: true,
      data: metadata,
      dataWithCounts: metadataWithCounts,
      config, // 返回配置信息，便于前端使用
      fieldInfo: metaFields.map(f => ({
        fieldName: f.fieldName,
        displayName: f.displayName,
        fieldType: f.fieldType,
        options: metadata[f.fieldName]?.slice(0, 20) || [] // 限制选项数量
      })),
      databaseInfo: {
        code: database,
        totalFields: metaFields.length,
        // 可以添加更多数据库信息
      }
    });

  } catch (__error) {
    console.error('Meta API Error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch metadata' },
      { status: 500 }
    );
  }
} 