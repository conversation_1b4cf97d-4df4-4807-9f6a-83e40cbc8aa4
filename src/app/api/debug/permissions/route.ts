import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseAccessLevel } from '@/lib/permissions';
import { checkPermissions } from '@/lib/server/permissions';

export async function GET(__request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const database = searchParams.get('database') || 'us_class';
    
    console.error('🔍 [Debug] Starting permission debugging:', { database });
    
    // 1. 测试 getDatabaseAccessLevel
    const accessLevel = await getDatabaseAccessLevel(database);
    console.error('🔍 [Debug] getDatabaseAccessLevel result:', accessLevel);
    
    // 2. 测试 checkPermissions
    const hasAccess = await checkPermissions(accessLevel);
    console.error('🔍 [Debug] checkPermissions result:', hasAccess);
    
    return NextResponse.json({
      success: true,
      debug: {
        database,
        accessLevel,
        hasAccess,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (__error) {
    console.error('🔍 [Debug] 权限调试失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
