import { NextRequest, NextResponse } from 'next/server';

// Dynamic import for @napi-rs/canvas (modern, zero-dependency canvas)
let createCanvas: ((width: number, height: number) => any) | null = null;
try {
  // Try @napi-rs/canvas first (preferred)
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  createCanvas = require('@napi-rs/canvas').createCanvas;
  console.error('Using @napi-rs/canvas for high-performance captcha generation');
} catch (__error) {
  try {
    // Fallback to traditional canvas
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    createCanvas = require('canvas').createCanvas;
    console.error('Using traditional canvas package');
  } catch (_fallbackError) {
    console.error('No canvas package available, using fallback captcha methods');
  }
}

// Store captcha sessions in memory (in production, use Redis)
const captchaSessions = new Map<string, { code: string; timestamp: number; attempts: number }>();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes
const MAX_ATTEMPTS = 5; // Maximum verification attempts

// Clean expired sessions
setInterval(() => {
  const now = Date.now();
  for (const [sessionId, session] of captchaSessions.entries()) {
    if (now - session.timestamp > SESSION_TIMEOUT) {
      captchaSessions.delete(sessionId);
    }
  }
}, 5 * 60 * 1000); // Clean every 5 minutes

function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function generateCaptchaCode(): string {
  // Exclude confusing characters: 0, O, 1, l, I
  const chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
  let result = "";
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function generateMathCaptcha(): { question: string; answer: number } {
  const operations = [
    () => {
      const a = Math.floor(Math.random() * 20) + 1;
      const b = Math.floor(Math.random() * 20) + 1;
      return { question: `${a} + ${b}`, answer: a + b };
    },
    () => {
      const a = Math.floor(Math.random() * 30) + 10;
      const b = Math.floor(Math.random() * 10) + 1;
      return { question: `${a} - ${b}`, answer: a - b };
    },
    () => {
      const a = Math.floor(Math.random() * 12) + 1;
      const b = Math.floor(Math.random() * 12) + 1;
      return { question: `${a} × ${b}`, answer: a * b };
    },
    () => {
      const b = Math.floor(Math.random() * 9) + 2;
      const answer = Math.floor(Math.random() * 15) + 1;
      const a = b * answer;
      return { question: `${a} ÷ ${b}`, answer };
    }
  ];
  
  return operations[Math.floor(Math.random() * operations.length)]();
}

function generateLogicCaptcha(): { question: string; answer: string } {
  const questions = [
    {
      question: "What comes next in the sequence: 2, 4, 6, 8, ?",
      answer: "10"
    },
    {
      question: "If today is Monday, what day will it be in 3 days?",
      answer: "thursday"
    },
    {
      question: "How many letters are in the word 'COMPUTER'?",
      answer: "8"
    },
    {
      question: "What is the opposite of 'hot'?",
      answer: "cold"
    },
    {
      question: "Complete: cat, dog, bird, ?",
      answer: "fish"
    },
    {
      question: "What color do you get when you mix red and blue?",
      answer: "purple"
    },
    {
      question: "How many sides does a triangle have?",
      answer: "3"
    },
    {
      question: "What is 2 to the power of 3?",
      answer: "8"
    }
  ];
  
  return questions[Math.floor(Math.random() * questions.length)];
}

// Enhanced anti-bot canvas image generation
async function generateCaptchaImage(code: string): Promise<Buffer> {
  // Check if canvas is available
  if (!createCanvas) {
    throw new Error('Canvas package not available');
  }

  try {
    const canvas = createCanvas(200, 80);
    const ctx = canvas.getContext('2d');

    // Dynamic background with gradient
    const gradient = ctx.createLinearGradient(0, 0, 200, 80);
    gradient.addColorStop(0, '#f8f9fa');
    gradient.addColorStop(1, '#e9ecef');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 200, 80);

    // Anti-bot noise generation (server-side only)
    for (let i = 0; i < 150; i++) {
      ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.15)`;
      ctx.fillRect(Math.random() * 200, Math.random() * 80, 1, 1);
    }

    // Complex interference lines (harder to reverse engineer)
    for (let i = 0; i < 12; i++) {
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.4)`;
      ctx.lineWidth = Math.random() * 3 + 1;
      ctx.beginPath();
      ctx.moveTo(Math.random() * 200, Math.random() * 80);
      ctx.quadraticCurveTo(Math.random() * 200, Math.random() * 80, Math.random() * 200, Math.random() * 80);
      ctx.stroke();
    }

    // Anti-OCR text rendering
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    for (let i = 0; i < code.length; i++) {
      const char = code[i];
      const x = 35 + i * 25 + Math.random() * 15 - 7.5;
      const y = 40 + Math.random() * 15 - 7.5;

      ctx.save();
      ctx.translate(x, y);
      ctx.rotate((Math.random() - 0.5) * 0.6); // More rotation

      // Dynamic color selection
      const colors = ['#2563eb', '#dc2626', '#059669', '#7c2d12', '#4338ca', '#be185d'];
      ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];

      // Enhanced shadow for anti-OCR
      ctx.shadowColor = 'rgba(0,0,0,0.4)';
      ctx.shadowBlur = 3;
      ctx.shadowOffsetX = Math.random() * 4 - 2;
      ctx.shadowOffsetY = Math.random() * 4 - 2;

      ctx.fillText(char, 0, 0);
      ctx.restore();
    }

    // Additional server-side only interference
    for (let i = 0; i < 8; i++) {
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
      ctx.lineWidth = Math.random() * 2 + 0.5;
      ctx.beginPath();
      ctx.moveTo(0, Math.random() * 80);
      ctx.lineTo(200, Math.random() * 80);
      ctx.stroke();
    }

    return canvas.toBuffer('image/png');
  } catch (__error) {
    console.error('Canvas generation failed:', error);
    throw error;
  }
}

export async function GET(__request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type') || 'text';
  const sessionId = generateSessionId();

  try {
    if (type === 'image') {
      const code = generateCaptchaCode();
      captchaSessions.set(sessionId, { code, timestamp: Date.now() });

      try {
        const imageBuffer = await generateCaptchaImage(code);
        
        return new NextResponse(imageBuffer, {
          headers: {
            'Content-Type': 'image/png',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'X-Captcha-Session': sessionId,
          },
        });
      } catch (_canvasError) {
        // Fallback to text captcha if canvas is not available
        return NextResponse.json({
          type: 'text',
          sessionId,
          code, // In production, don't send the actual code
          question: `Enter this code: ${code}`,
        });
      }
    } else if (type === 'math') {
      const { question, answer } = generateMathCaptcha();
      captchaSessions.set(sessionId, { code: answer.toString(), timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'math',
        sessionId,
        question: `${question} = ?`,
      });
    } else if (type === 'logic') {
      const { question, answer } = generateLogicCaptcha();
      captchaSessions.set(sessionId, { code: answer.toLowerCase(), timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'logic',
        sessionId,
        question,
      });
    } else {
      // Default text captcha
      const code = generateCaptchaCode();
      captchaSessions.set(sessionId, { code, timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'text',
        sessionId,
        question: `Enter this code: ${code}`,
      });
    }
  } catch (__error) {
    console.error('Captcha generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate captcha' },
      { status: 500 }
    );
  }
}

export async function POST(__request: NextRequest) {
  try {
    const { sessionId, answer } = await request.json();

    if (!sessionId || !answer) {
      return NextResponse.json(
        { error: 'Session ID and answer are required' },
        { status: 400 }
      );
    }

    const session = captchaSessions.get(sessionId);
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid or expired captcha session' },
        { status: 400 }
      );
    }

    // Check if session is expired
    if (Date.now() - session.timestamp > SESSION_TIMEOUT) {
      captchaSessions.delete(sessionId);
      return NextResponse.json(
        { error: 'Captcha session expired' },
        { status: 400 }
      );
    }

    // Validate answer (case-insensitive)
    const isValid = answer.toLowerCase().trim() === session.code.toLowerCase();
    
    if (isValid) {
      // Remove session after successful validation
      captchaSessions.delete(sessionId);
    }

    return NextResponse.json({
      valid: isValid,
      message: isValid ? 'Captcha verified successfully' : 'Incorrect answer'
    });

  } catch (__error) {
    console.error('Captcha validation error:', error);
    return NextResponse.json(
      { error: 'Failed to validate captcha' },
      { status: 500 }
    );
  }
}
