import { db } from './prisma';

// 表映射配置接口
export interface TableMapping {
  modelName?: string;
  tableName?: string;
  displayName: string;
  category?: string;
  description?: string;
}

/**
 * 静态表映射服务
 * 应用启动时一次性加载所有配置到内存，运行时纯内存操作
 */
export class StaticTableMappingService {
  private static configMap: Map<string, TableMapping> = new Map();
  private static isInitialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * 初始化配置服务（应用启动时调用）
   */
  static async initialize(): Promise<void> {
    // 防止重复初始化
    if (this.isInitialized) {
      return;
    }

    // 防止并发初始化
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  private static async _doInitialize(): Promise<void> {
    try {
      console.error('[StaticTableMappingService] 开始加载数据库配置...');
      
      // 从数据库加载所有激活的配置
      const configs = await db.databaseConfig.findMany({
        where: { isActive: true },
        select: {
          code: true,
          name: true,
          category: true,
          description: true,
          tableName: true,
          modelName: true
        },
        orderBy: { sortOrder: 'asc' }
      });

      // 清空现有配置
      this.configMap.clear();

      // 加载配置到内存
      for (const config of configs) {
        const mapping: TableMapping = {
          modelName: config.modelName || undefined,
          tableName: config.tableName || undefined,
          displayName: config.name,
          category: config.category || undefined,
          description: config.description || undefined
        };

        this.configMap.set(config.code, mapping);
      }

      this.isInitialized = true;
      this.initializationPromise = null;

      console.error(`[StaticTableMappingService] ✅ 成功加载 ${configs.length} 个数据库配置`);
      
      // 输出已加载的配置列表
      if (configs.length > 0) {
        console.error('[StaticTableMappingService] 已加载的配置:');
        configs.forEach(config => {
          console.error(`  - ${config.code}: ${config.name} (表: ${config.tableName || 'N/A'}, 模型: ${config.modelName || 'N/A'})`);
        });
      }

    } catch (__error) {
      this.isInitialized = false;
      this.initializationPromise = null;
      console.error('[StaticTableMappingService] ❌ 配置加载失败:', error);
      throw error;
    }
  }

  /**
   * 获取表映射配置（纯内存操作，0ms）
   */
  static getTableMapping(databaseCode: string): TableMapping | null {
    if (!this.isInitialized) {
      console.warn('[StaticTableMappingService] 配置尚未初始化，请先调用 initialize()');
      return null;
    }

    return this.configMap.get(databaseCode) || null;
  }

  /**
   * 获取Prisma模型实例
   */
  static getDynamicModel(databaseCode: string) {
    // 特殊处理：us_class 使用固定的 uSClass 模型
    if (databaseCode === 'us_class') {
      const model = (db as any).uSClass;
      if (!model || !this.isPrismaModel(model)) {
        console.error(`[StaticTableMappingService] uSClass 模型不存在或无效`);
        return null;
      }
      console.error(`[StaticTableMappingService] 为 us_class 使用固定 uSClass 模型`);
      return model;
    }

    const mapping = this.getTableMapping(databaseCode);
    if (!mapping?.modelName) {
      console.warn(`[StaticTableMappingService] 数据库代码 ${databaseCode} 没有配置模型名称`);
      return null;
    }

    // 根据模型名称获取Prisma模型
    const modelName = mapping.modelName;
    const model = (db as any)[modelName];

    if (!model || !this.isPrismaModel(model)) {
      console.error(`[StaticTableMappingService] Prisma模型 ${modelName} 不存在或无效`);
      return null;
    }

    return model;
  }

  /**
   * 验证数据库代码是否有效
   */
  static validateDatabaseCode(databaseCode: string): boolean {
    // 检查配置中是否存在
    if (this.configMap.has(databaseCode)) {
      return true;
    }
    
    // 特殊处理：us_class 使用固定的 USClass 模型
    if (databaseCode === 'us_class') {
      console.error('[StaticTableMappingService] 为 us_class 使用固定映射');
      return true;
    }
    
    return false;
  }

  /**
   * 获取所有数据库代码
   */
  static getAllDatabaseCodes(): string[] {
    return Array.from(this.configMap.keys());
  }

  /**
   * 获取所有配置
   */
  static getAllMappings(): Record<string, TableMapping> {
    const result: Record<string, TableMapping> = {};
    this.configMap.forEach((mapping, code) => {
      result[code] = mapping;
    });
    return result;
  }

  /**
   * 手动刷新配置（重新从数据库加载）
   */
  static async refresh(): Promise<void> {
    console.error('[StaticTableMappingService] 🔄 手动刷新配置...');
    this.isInitialized = false;
    this.initializationPromise = null;
    await this.initialize();
  }

  /**
   * 获取初始化状态
   */
  static getInitializationStatus(): {
    initialized: boolean;
    configCount: number;
    configCodes: string[];
  } {
    return {
      initialized: this.isInitialized,
      configCount: this.configMap.size,
      configCodes: Array.from(this.configMap.keys())
    };
  }

  /**
   * 检查对象是否为有效的Prisma模型
   */
  private static isPrismaModel(model: unknown): model is { 
    findMany: (args?: unknown) => unknown; 
    groupBy: (args?: unknown) => unknown;
    findUnique: (args?: unknown) => unknown;
    create: (args?: unknown) => unknown;
    update: (args?: unknown) => unknown;
    delete: (args?: unknown) => unknown;
    count: (args?: unknown) => unknown;
  } {
    return (
      typeof model === 'object' &&
      model !== null &&
      typeof (model as any).findMany === 'function' &&
      typeof (model as any).groupBy === 'function' &&
      typeof (model as any).count === 'function'
    );
  }
}

// 向后兼容的导出函数，保持现有API不变
export async function getTableMapping(databaseCode: string): Promise<TableMapping | null> {
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  return StaticTableMappingService.getTableMapping(databaseCode);
}

export async function getDynamicModel(databaseCode: string) {
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  return StaticTableMappingService.getDynamicModel(databaseCode);
}

export async function validateDatabaseCode(databaseCode: string): Promise<{
  isValid: boolean;
  error?: string;
  status?: number;
}> {
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  
  const isValid = StaticTableMappingService.validateDatabaseCode(databaseCode);
  if (!isValid) {
    return {
      isValid: false,
      error: `数据库代码 '${databaseCode}' 无效或未配置`,
      status: 400
    };
  }
  
  return { isValid: true };
}

export async function getAllDatabaseCodes(): Promise<string[]> {
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  return StaticTableMappingService.getAllDatabaseCodes();
}

export function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return (
    typeof model === 'object' &&
    model !== null &&
    typeof (model as any).findMany === 'function' &&
    typeof (model as any).groupBy === 'function'
  );
} 