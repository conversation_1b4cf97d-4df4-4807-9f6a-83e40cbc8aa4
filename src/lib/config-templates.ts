import { DatabaseFieldConfig } from './configCache';

/**
 * 配置模板系统
 * 提供常用的字段配置模板，便于快速创建新数据库配置
 */

export interface ConfigTemplate {
  name: string;
  description: string;
  fields: Omit<DatabaseFieldConfig, 'id' | 'databaseCode' | 'createdAt' | 'updatedAt'>[];
}

/**
 * 医疗器械数据库模板
 */
export const MEDICAL_DEVICE_TEMPLATE: ConfigTemplate = {
  name: '医疗器械数据库',
  description: '适用于医疗器械注册、审批等相关数据库',
  fields: [
    {
      fieldName: 'productName',
      displayName: '产品名称',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: true,
      isSortable: true,
      sortOrder: 1,
      listOrder: 1,
      detailOrder: 1,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'companyName',
      displayName: '公司名称',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: true,
      isSortable: true,
      sortOrder: 2,
      listOrder: 2,
      detailOrder: 2,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'registrationNumber',
      displayName: '注册证号',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: false,
      isSortable: true,
      sortOrder: 3,
      listOrder: 3,
      detailOrder: 3,
      searchType: 'exact',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'deviceclass',
      displayName: '器械类别',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 4,
      listOrder: 4,
      detailOrder: 4,
      searchType: 'exact',
      filterType: 'multi_select', // 默认多选
      isActive: true,
    },
    {
      fieldName: 'category',
      displayName: '产品分类',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 5,
      listOrder: 5,
      detailOrder: 5,
      searchType: 'exact',
      filterType: 'multi_select',
      isActive: true,
    },
    {
      fieldName: 'status',
      displayName: '状态',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 6,
      listOrder: 6,
      detailOrder: 6,
      searchType: 'exact',
      filterType: 'select', // 状态通常单选
      isActive: true,
    },
    {
      fieldName: 'approvalDate',
      displayName: '批准日期',
      fieldType: 'date',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 7,
      listOrder: 7,
      detailOrder: 7,
      searchType: 'date_range',
      filterType: 'date_range',
      isActive: true,
    },
    {
      fieldName: 'description',
      displayName: '产品描述',
      fieldType: 'text',
      isVisible: false, // 详情页显示
      isSearchable: true,
      isFilterable: false,
      isSortable: false,
      sortOrder: 8,
      listOrder: 0, // 不在列表显示
      detailOrder: 8,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
  ],
};

/**
 * 药品数据库模板
 */
export const DRUG_TEMPLATE: ConfigTemplate = {
  name: '药品数据库',
  description: '适用于药品注册、审批等相关数据库',
  fields: [
    {
      fieldName: 'drugName',
      displayName: '药品名称',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: true,
      isSortable: true,
      sortOrder: 1,
      listOrder: 1,
      detailOrder: 1,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'activeIngredient',
      displayName: '活性成分',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: true,
      isSortable: true,
      sortOrder: 2,
      listOrder: 2,
      detailOrder: 2,
      searchType: 'contains',
      filterType: 'multi_select', // 可能有多个成分
      isActive: true,
    },
    {
      fieldName: 'dosageForm',
      displayName: '剂型',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 3,
      listOrder: 3,
      detailOrder: 3,
      searchType: 'exact',
      filterType: 'multi_select',
      isActive: true,
    },
    {
      fieldName: 'therapeuticArea',
      displayName: '治疗领域',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 4,
      listOrder: 4,
      detailOrder: 4,
      searchType: 'exact',
      filterType: 'multi_select',
      isActive: true,
    },
    {
      fieldName: 'approvalStatus',
      displayName: '审批状态',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 5,
      listOrder: 5,
      detailOrder: 5,
      searchType: 'exact',
      filterType: 'select',
      isActive: true,
    },
  ],
};

/**
 * 专利数据库模板
 */
export const PATENT_TEMPLATE: ConfigTemplate = {
  name: '专利数据库',
  description: '适用于专利信息相关数据库',
  fields: [
    {
      fieldName: 'patentNumber',
      displayName: '专利号',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: false,
      isSortable: true,
      sortOrder: 1,
      listOrder: 1,
      detailOrder: 1,
      searchType: 'exact',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'title',
      displayName: '专利标题',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: false,
      isSortable: true,
      sortOrder: 2,
      listOrder: 2,
      detailOrder: 2,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'applicant',
      displayName: '申请人',
      fieldType: 'text',
      isVisible: true,
      isSearchable: true,
      isFilterable: true,
      isSortable: true,
      sortOrder: 3,
      listOrder: 3,
      detailOrder: 3,
      searchType: 'contains',
      filterType: 'input',
      isActive: true,
    },
    {
      fieldName: 'patentType',
      displayName: '专利类型',
      fieldType: 'select',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 4,
      listOrder: 4,
      detailOrder: 4,
      searchType: 'exact',
      filterType: 'multi_select',
      isActive: true,
    },
    {
      fieldName: 'applicationDate',
      displayName: '申请日期',
      fieldType: 'date',
      isVisible: true,
      isSearchable: false,
      isFilterable: true,
      isSortable: true,
      sortOrder: 5,
      listOrder: 5,
      detailOrder: 5,
      searchType: 'date_range',
      filterType: 'date_range',
      isActive: true,
    },
  ],
};

/**
 * 所有可用模板
 */
export const CONFIG_TEMPLATES = {
  medicalDevice: MEDICAL_DEVICE_TEMPLATE,
  drug: DRUG_TEMPLATE,
  patent: PATENT_TEMPLATE,
} as const;

/**
 * 获取模板列表
 */
export function getTemplateList(): Array<{ key: string; name: string; description: string }> {
  return Object.entries(CONFIG_TEMPLATES).map(([key, template]) => ({
    key,
    name: template.name,
    description: template.description,
  }));
}

/**
 * 根据模板创建字段配置
 */
export function createFieldsFromTemplate(
  templateKey: keyof typeof CONFIG_TEMPLATES,
  databaseCode: string
): DatabaseFieldConfig[] {
  const template = CONFIG_TEMPLATES[templateKey];
  if (!template) {
    throw new Error(`模板不存在: ${templateKey}`);
  }

  return template.fields.map((field, _index) => ({
    ...field,
    id: 0, // 将由数据库生成
    databaseCode,
    createdAt: new Date(),
    updatedAt: new Date(),
  })) as DatabaseFieldConfig[];
}
