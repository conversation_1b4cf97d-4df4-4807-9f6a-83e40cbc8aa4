
// 轻量级权限检查辅助函数
// 注意：这个函数应该被废弃，建议使用动态权限检查
export function hasPermissionSync(membershipType: string, database: string, action = 'read') {
  console.warn('⚠️ hasPermissionSync is deprecated. Use dynamic permission checking instead.');

  // 临时兼容性实现 - 应该从数据库配置中读取
  // TODO: 重构为异步函数，从数据库读取配置

  // 暂时允许所有已知数据库的访问，避免硬编码
  const knownDatabases = ['us_class', 'us_pmn', 'freepat', 'devicecnimported'];

  if (knownDatabases.includes(database.toLowerCase())) {
    // 对于已知数据库，暂时都允许访问
    // 实际权限检查应该在服务器端进行
    return true;
  }

  // 未知数据库默认拒绝访问
  return false;
}

// 新的异步权限检查函数
export async function hasPermissionAsync(membershipType: string, database: string, action = 'read'): Promise<boolean> {
  try {
    const response = await fetch('/api/config/databases');
    if (!response.ok) {
      console.warn('Failed to fetch database config, falling back to sync check');
      return hasPermissionSync(membershipType, database, action);
    }

    const result = await response.json();
    if (!result.success || !result.data[database]) {
      console.warn(`Database ${database} not found in config`);
      return false;
    }

    const dbConfig = result.data[database];
    const requiredLevel = dbConfig.accessLevel || 'premium';

    // 免费数据库所有人都可以访问
    if (requiredLevel === 'free') {
      return true;
    }

    // 检查用户权限级别
    if (requiredLevel === 'premium') {
      return membershipType === 'premium' || membershipType === 'enterprise';
    }

    if (requiredLevel === 'enterprise') {
      return membershipType === 'enterprise';
    }

    return false;
  } catch (__error) {
    console.error('Error checking permissions:', error);
    return hasPermissionSync(membershipType, database, action);
  }
}

export function getQuotaLimits(membershipType: string) {
  const quotas = {
    free: { dailyQueries: 50, exportLimit: 0 },
    premium: { dailyQueries: 1000, exportLimit: 500 },
    enterprise: { dailyQueries: -1, exportLimit: 5000 },
  };
  
  return quotas[membershipType as keyof typeof quotas] || quotas.free;
}
