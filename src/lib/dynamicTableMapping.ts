import { db } from './prisma';
import { DynamicTableMappingService, type TableMapping as DynamicTableMapping } from './dynamicTableMappingService';

/**
 * 数据库代码到模型映射的接口定义
 * @deprecated 使用 DynamicTableMappingService 中的 TableMapping
 */
export interface TableMapping {
  modelName: string;
  tableName: string;
  displayName: string;
  category: string;
  description: string;
}

/**
 * 硬编码的数据库代码到Prisma模型的映射配置
 * 作为备选方案，当数据库配置不可用时使用
 * @deprecated 现在优先使用数据库中的配置，这个映射仅作为备选
 */
export const DATABASE_TABLE_MAPPING: Record<string, TableMapping> = {
  // 中国大陆医疗器械数据库
  deviceCNImported: {
    modelName: 'medicalDevice_CN_Imported',
    tableName: 'medical_device_cn_imported',
    displayName: '医疗器械模板',
    category: 'Regulation',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构'
  },
  
  deviceCNEvaluation: {
    modelName: 'medicalDevice_CN_Evaluation',
    tableName: 'medical_device_cn_evaluation',
    displayName: '中国大陆审评',
    category: 'Regulation',
    description: '医疗器械审评进度跟踪、审评结论查询'
  },

  // 国际医疗器械数据库
  deviceUS: {
    modelName: 'medicalDevice_US',
    tableName: 'medical_device_us',
    displayName: '美国上市',
    category: '全球器械',
    description: '美国FDA批准的医疗器械信息'
  },

  deviceHK: {
    modelName: 'medicalDevice_HK',
    tableName: 'medical_device_hk',
    displayName: '中国香港上市',
    category: 'Regulation',
    description: '中国香港已上市的医疗器械信息'
  },

  deviceJP: {
    modelName: 'medicalDevice_JP',
    tableName: 'medical_device_jp',
    displayName: '日本上市',
    category: '全球器械',
    description: '日本PMDA批准的医疗器械信息'
  },

  deviceUK: {
    modelName: 'medicalDevice_UK',
    tableName: 'medical_device_uk',
    displayName: '英国上市',
    category: '全球器械',
    description: '英国MHRA批准的医疗器械信息'
  },

  deviceSG: {
    modelName: 'medicalDevice_SG',
    tableName: 'medical_device_sg',
    displayName: '新加坡上市',
    category: '全球器械',
    description: '新加坡HSA批准的医疗器械信息'
  },

  // 美国PMN(510k)数据库 - 专用模型示例
  us_pmn: {
    modelName: 'medicalDevice_US_PMN',
    tableName: 'medical_device_us_pmn',
    displayName: '美国PMN(510k)',
    category: '美国器械',
    description: '美国FDA 510(k)预市场通知数据库，包含K号、申请人、审批状态等PMN特有信息'
  },

  // 专利数据库
  freePat: {
    modelName: 'medicalDevice_FreePat',
    tableName: 'medical_device_free_pat',
    displayName: '医药专利',
    category: '药物研发',
    description: '医药专利信息及原文下载'
  },

  // 专题数据库
  subjectNewdrug: {
    modelName: 'medicalDevice_SubjectNewdrug',
    tableName: 'medical_device_subject_newdrug',
    displayName: '全球获批新药',
    category: '专题数据',
    description: '全球范围内获得批准的新药信息'
  },

  subjectLicenseout: {
    modelName: 'medicalDevice_SubjectLicenseout',
    tableName: 'medical_device_subject_licenseout',
    displayName: 'License out交易',
    category: '专题数据',
    description: 'License out交易数据'
  },

  subjectVbp: {
    modelName: 'medicalDevice_SubjectVbp',
    tableName: 'medical_device_subject_vbp',
    displayName: '国家集采结果',
    category: '专题数据',
    description: '国家集采结果数据'
  }
};

/**
 * 获取数据库映射配置
 * 优先从数据库获取，失败时使用硬编码备选
 * @param databaseCode 数据库代码
 * @returns 映射配置对象
 */
export async function getTableMapping(databaseCode: string): Promise<TableMapping | null> {
  try {
    // 优先使用新的动态服务
    const dynamicMapping = await DynamicTableMappingService.getTableMapping(databaseCode);
    if (dynamicMapping) {
      return dynamicMapping;
    }
  } catch (__error) {
    console.warn(`[getTableMapping] 从数据库获取配置失败，使用备选方案: ${error}`);
  }

  // 备选：使用硬编码映射
  const fallbackMapping = DATABASE_TABLE_MAPPING[databaseCode];
  if (fallbackMapping) {
    console.warn(`[getTableMapping] 使用硬编码备选配置: ${databaseCode}`);
    return fallbackMapping;
  }

  return null;
}

/**
 * 动态获取Prisma模型实例
 * 这个函数替代了之前在API路由中硬编码的模型获取逻辑
 * @param databaseCode 数据库代码
 * @returns Prisma模型实例
 */
export async function getDynamicModel(databaseCode: string) {
  try {
    // 优先使用新的动态服务
    return await DynamicTableMappingService.getDynamicModel(databaseCode);
  } catch (__error) {
    console.warn(`[getDynamicModel] 动态服务失败，尝试备选方案: ${error}`);
    
    // 备选：使用硬编码映射
    const mapping = DATABASE_TABLE_MAPPING[databaseCode];
    if (!mapping) {
      throw new Error(`不支持的数据库代码: ${databaseCode}. 支持的代码: ${Object.keys(DATABASE_TABLE_MAPPING).join(', ')}`);
    }

    // 通过动态属性访问获取对应的Prisma模型
    const model = (db as any)[mapping.modelName];
    if (!model) {
      throw new Error(`找不到模型: ${mapping.modelName}`);
    }

    return model;
  }
}

/**
 * 验证数据库代码是否有效
 * @param databaseCode 数据库代码
 * @returns 验证结果
 */
export async function validateDatabaseCode(databaseCode: string): Promise<{ isValid: boolean; error?: string; status?: number }> {
  try {
    // 优先使用新的动态服务
    return await DynamicTableMappingService.validateDatabaseCode(databaseCode);
  } catch (__error) {
    console.warn(`[validateDatabaseCode] 动态服务失败，使用备选验证: ${error}`);
    
    // 备选：检查硬编码映射
    if (!databaseCode || typeof databaseCode !== 'string') {
      return {
        isValid: false,
        error: '数据库代码不能为空',
        status: 400
      };
    }

    if (DATABASE_TABLE_MAPPING[databaseCode]) {
      return { isValid: true };
    }

    return {
      isValid: false,
      error: `不支持的数据库代码: ${databaseCode}`,
      status: 404
    };
  }
}

/**
 * 获取所有可用的数据库代码
 * @returns 数据库代码数组
 */
export async function getAllDatabaseCodes(): Promise<string[]> {
  try {
    // 优先使用新的动态服务
    const dynamicCodes = await DynamicTableMappingService.getAllDatabaseCodes();
    if (dynamicCodes.length > 0) {
      return dynamicCodes;
    }
  } catch (__error) {
    console.warn(`[getAllDatabaseCodes] 动态服务失败，使用备选列表: ${error}`);
  }

  // 备选：返回硬编码的数据库代码
  return Object.keys(DATABASE_TABLE_MAPPING);
}

/**
 * 检查Prisma模型是否有效
 * @param model Prisma模型实例
 * @returns 是否为有效的Prisma模型
 */
export function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return DynamicTableMappingService.isPrismaModel(model);
}

/**
 * 获取数据库访问权限级别
 * 这个函数用于权限控制，替代硬编码的访问级别映射
 * @param databaseCode 数据库代码
 * @returns 访问权限级别
 */
export function getDatabaseAccessLevel(databaseCode: string): string {
  // 这里可以从数据库配置中动态获取，或者使用默认规则
  const freeDatabase = ['freePat', 'deviceCNEvaluation', 'deviceCNImported'];
  const premiumDatabase = ['deviceHK', 'deviceUS', 'deviceJP', 'deviceUK', 'deviceSG', 'subjectNewdrug'];
  const enterpriseDatabase = ['subjectLicenseout', 'subjectVbp'];

  if (freeDatabase.includes(databaseCode)) return 'free';
  if (premiumDatabase.includes(databaseCode)) return 'premium';
  if (enterpriseDatabase.includes(databaseCode)) return 'enterprise';
  
  return 'premium'; // 默认需要高级权限
}

/**
 * 为分析和日志功能提供的辅助函数
 * 从路径中提取数据库代码
 * @param path URL路径
 * @returns 提取的数据库代码，如果无法提取则返回null
 */
export async function extractDatabaseCodeFromPath(path: string): Promise<string | null> {
  // 匹配 /data/list/[database] 或 /api/data/[database] 等模式
  const matches = path.match(/\/(?:data\/list|api\/data|api\/meta|api\/stats|api\/export|api\/advanced-search)\/([^\/\?]+)/);
  if (matches && matches[1]) {
    const validation = await validateDatabaseCode(matches[1]);
    if (validation.isValid) {
      return matches[1];
    }
  }
  return null;
}

// 向后兼容的导出
export { DynamicTableMappingService }; 