import { db } from '@/lib/prisma';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class AnalyticsCache {
  private cache = new Map<string, CacheEntry<Record<string, unknown>>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5分钟默认缓存

  // 生成缓存键
  private generateKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    return `${prefix}:${sortedParams}`;
  }

  // 检查缓存是否有效
  private isValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  // 获取缓存数据
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry || !this.isValid(entry)) {
      this.cache.delete(key);
      return null;
    }
    return entry.data;
  }

  // 设置缓存数据
  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  // 清除过期缓存
  cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (!this.isValid(entry)) {
        this.cache.delete(key);
      }
    }
  }

  // 清除所有缓存
  clear(): void {
    this.cache.clear();
  }

  // 获取缓存统计信息
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 单例实例
export const analyticsCache = new AnalyticsCache();

// 缓存装饰器函数
export function withCache<T>(
  cacheKey: string,
  ttl = 5 * 60 * 1000
) {
  return function(target: Record<string, unknown>, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function(...args: Record<string, unknown>[]): Promise<T> {
      const key = `${cacheKey}:${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cached = analyticsCache.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      analyticsCache.set(key, result, ttl);
      
      return result;
    };
  };
}

// 高频查询的缓存函数
export class AnalyticsQueries {
  
  // 缓存基础统计数据
  static async getCachedBasicStats(timeRange: string, database?: string) {
    const key = analyticsCache.generateKey('basic_stats', { timeRange, database });
    let stats = analyticsCache.get(key);
    
    if (!stats) {
      stats = await this.calculateBasicStats(timeRange, database);
      analyticsCache.set(key, stats, 2 * 60 * 1000); // 2分钟缓存
    }
    
    return stats;
  }

  // 计算基础统计数据（优化版）
  private static async calculateBasicStats(timeRange: string, database?: string) {
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    const whereCondition: Record<string, unknown> = {
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (database) {
      whereCondition.database = database;
    }

    // 使用原生SQL进行优化查询
    let basicStats;
    if (database) {
      [basicStats] = await db.$queryRaw<Array<{
        total_visits: bigint;
        unique_visitors: bigint;
        page_views: bigint;
        search_events: bigint;
      }>>`
        SELECT
          COUNT(*) as total_visits,
          COUNT(DISTINCT ip) as unique_visitors,
          COUNT(*) FILTER (WHERE "eventType" = 'pageview') as page_views,
          COUNT(*) FILTER (WHERE "eventType" IN ('database_search', 'advanced_search')) as search_events
        FROM "ActivityLog"
        WHERE "createdAt" >= ${startDate}
          AND "createdAt" <= ${now}
          AND "database" = ${database}
      `;
    } else {
      [basicStats] = await db.$queryRaw<Array<{
        total_visits: bigint;
        unique_visitors: bigint;
        page_views: bigint;
        search_events: bigint;
      }>>`
        SELECT
          COUNT(*) as total_visits,
          COUNT(DISTINCT ip) as unique_visitors,
          COUNT(*) FILTER (WHERE "eventType" = 'pageview') as page_views,
          COUNT(*) FILTER (WHERE "eventType" IN ('database_search', 'advanced_search')) as search_events
        FROM "ActivityLog"
        WHERE "createdAt" >= ${startDate}
          AND "createdAt" <= ${now}
      `;
    }

    return {
      totalVisits: Number(basicStats.total_visits),
      uniqueVisitors: Number(basicStats.unique_visitors),
      pageViews: Number(basicStats.page_views),
      searchEvents: Number(basicStats.search_events),
    };
  }

  // 缓存热门数据库统计
  static async getCachedTopDatabases(timeRange: string, limit = 0) {
    const key = analyticsCache.generateKey('top_databases', { timeRange, limit });
    let stats = analyticsCache.get(key);
    
    if (!stats) {
      stats = await this.calculateTopDatabases(timeRange, limit);
      analyticsCache.set(key, stats, 5 * 60 * 1000); // 5分钟缓存
    }
    
    return stats;
  }

  private static async calculateTopDatabases(timeRange: string, limit: number) {
    const now = new Date();
    const startDate = this.getStartDate(timeRange, now);

    const topDatabases = await db.$queryRaw<Array<{
      database: string;
      count: bigint;
    }>>`
      SELECT 
        "database",
        COUNT(*) as count
      FROM "ActivityLog" 
      WHERE "createdAt" >= ${startDate} 
        AND "createdAt" <= ${now}
        AND "database" IS NOT NULL
      GROUP BY "database"
      ORDER BY count DESC
      LIMIT ${limit}
    `;

    return topDatabases.map(item => ({
      name: item.database,
      visits: Number(item.count)
    }));
  }

  // 优化的跳出率计算
  static async getCachedBounceRate(timeRange: string, database?: string) {
    const key = analyticsCache.generateKey('bounce_rate', { timeRange, database });
    let bounceRate = analyticsCache.get(key);
    
    if (bounceRate === null) {
      bounceRate = await this.calculateBounceRate(timeRange, database);
      analyticsCache.set(key, bounceRate, 10 * 60 * 1000); // 10分钟缓存
    }
    
    return bounceRate;
  }

  private static async calculateBounceRate(timeRange: string, database?: string) {
    const now = new Date();
    const startDate = this.getStartDate(timeRange, now);

    // 使用SQL直接计算跳出率，避免内存中处理大量数据
    let bounceStats;
    if (database) {
      [bounceStats] = await db.$queryRaw<Array<{
        total_sessions: bigint;
        bounced_sessions: bigint;
      }>>`
        WITH session_counts AS (
          SELECT
            "sessionId",
            COUNT(*) as page_count
          FROM "ActivityLog"
          WHERE "createdAt" >= ${startDate}
            AND "createdAt" <= ${now}
            AND "sessionId" IS NOT NULL
            AND "database" = ${database}
          GROUP BY "sessionId"
        )
        SELECT
          COUNT(*) as total_sessions,
          COUNT(*) FILTER (WHERE page_count = 1) as bounced_sessions
        FROM session_counts
      `;
    } else {
      [bounceStats] = await db.$queryRaw<Array<{
        total_sessions: bigint;
        bounced_sessions: bigint;
      }>>`
        WITH session_counts AS (
          SELECT
            "sessionId",
            COUNT(*) as page_count
          FROM "ActivityLog"
          WHERE "createdAt" >= ${startDate}
            AND "createdAt" <= ${now}
            AND "sessionId" IS NOT NULL
          GROUP BY "sessionId"
        )
        SELECT
          COUNT(*) as total_sessions,
          COUNT(*) FILTER (WHERE page_count = 1) as bounced_sessions
        FROM session_counts
      `;
    }

    const totalSessions = Number(bounceStats.total_sessions);
    const bouncedSessions = Number(bounceStats.bounced_sessions);
    
    return totalSessions > 0 ? (bouncedSessions / totalSessions) * 100 : 0;
  }

  private static getStartDate(timeRange: string, now: Date): Date {
    switch (timeRange) {
      case '1d':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
  }
}

// 定期清理缓存
setInterval(() => {
  analyticsCache.cleanup();
}, 60 * 1000); // 每分钟清理一次过期缓存
