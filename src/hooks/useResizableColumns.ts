import { useState, useCallback, useRef, useEffect, useMemo } from 'react';

export interface ColumnWidth {
  fieldName: string;
  width: number;
  minWidth: number;
  maxWidth: number;
}

export interface UseResizableColumnsProps {
  initialColumns: Array<{
    fieldName: string;
    fieldType: string;
    displayName: string;
  }>;
}

export interface UseResizableColumnsReturn {
  columnWidths: Map<string, ColumnWidth>;
  isResizing: boolean;
  resizingColumn: string | null;
  handleMouseDown: (fieldName: string, event: React.MouseEvent) => void;
  getColumnStyle: (fieldName: string) => React.CSSProperties;
  resetColumnWidths: () => void;
}

// 根据字段类型和名称获取默认宽度
const getDefaultWidth = (fieldName: string, fieldType: string): number => {
  if (['productName', 'companyName'].includes(fieldName)) {
    return 240; // w-60
  } else if (['registrationNumber'].includes(fieldName)) {
    return 192; // w-48
  } else if (['structureOrUse', 'specifications', 'structure'].includes(fieldName)) {
    return 256; // w-64
  } else if (['category', 'managementType'].includes(fieldName)) {
    return 144; // w-36
  } else if (fieldType === 'date') {
    return 128; // w-32
  }
  return 160; // w-40 默认宽度
};

// 获取最小和最大宽度限制
const getWidthLimits = (fieldName: string, fieldType: string) => {
  const defaultWidth = getDefaultWidth(fieldName, fieldType);
  return {
    minWidth: Math.max(80, defaultWidth * 0.5), // 最小宽度不少于80px，或默认宽度的50%
    maxWidth: defaultWidth * 3 // 最大宽度为默认宽度的3倍
  };
};

export const useResizableColumns = ({
  initialColumns
}: UseResizableColumnsProps): UseResizableColumnsReturn => {
  // 初始化列宽度映射 - 使用 useMemo 避免每次渲染都创建新函数
  const initializeColumnWidths = useMemo(() => {
    const widthMap = new Map<string, ColumnWidth>();

    initialColumns.forEach(column => {
      const defaultWidth = getDefaultWidth(column.fieldName, column.fieldType);
      const { minWidth, maxWidth } = getWidthLimits(column.fieldName, column.fieldType);

      widthMap.set(column.fieldName, {
        fieldName: column.fieldName,
        width: defaultWidth,
        minWidth,
        maxWidth
      });
    });

    return widthMap;
  }, [initialColumns]);

  const [columnWidths, setColumnWidths] = useState<Map<string, ColumnWidth>>(() => initializeColumnWidths);
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<string | null>(null);
  
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);
  const resizingColumnRef = useRef<string | null>(null);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!resizingColumnRef.current) return;

    const deltaX = event.clientX - startXRef.current;
    const newWidth = startWidthRef.current + deltaX;

    setColumnWidths(prev => {
      const columnWidth = prev.get(resizingColumnRef.current!);
      if (!columnWidth) return prev;

      // 应用宽度限制
      const constrainedWidth = Math.max(
        columnWidth.minWidth,
        Math.min(columnWidth.maxWidth, newWidth)
      );

      const newMap = new Map(prev);
      const updatedColumn = { ...columnWidth, width: constrainedWidth };
      newMap.set(resizingColumnRef.current!, updatedColumn);
      return newMap;
    });
  }, []); // 移除 columnWidths 依赖，使用 prev 参数获取最新状态

  // 处理鼠标释放事件
  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setResizingColumn(null);
    resizingColumnRef.current = null;

    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // 恢复默认样式
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  }, [handleMouseMove]); // 添加 handleMouseMove 依赖

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((fieldName: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    setColumnWidths(prev => {
      const columnWidth = prev.get(fieldName);
      if (!columnWidth) return prev;

      setIsResizing(true);
      setResizingColumn(fieldName);
      resizingColumnRef.current = fieldName;
      startXRef.current = event.clientX;
      startWidthRef.current = columnWidth.width;

      // 添加全局鼠标事件监听器
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      // 防止文本选择
      document.body.style.userSelect = 'none';
      document.body.style.cursor = 'col-resize';

      return prev; // 不修改状态，只是为了获取最新的 columnWidth
    });
  }, [handleMouseMove, handleMouseUp]);

  // 获取列的样式
  const getColumnStyle = useCallback((fieldName: string): React.CSSProperties => {
    const columnWidth = columnWidths.get(fieldName);
    if (!columnWidth) return {};

    return {
      width: `${columnWidth.width}px`,
      minWidth: `${columnWidth.width}px`,
      maxWidth: `${columnWidth.width}px`,
      flexShrink: 0
    };
  }, [columnWidths]);

  // 重置列宽度
  const resetColumnWidths = useCallback(() => {
    setColumnWidths(initializeColumnWidths);
  }, [initializeColumnWidths]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
      document.body.style.cursor = '';
    };
  }, []); // 移除依赖，在组件卸载时清理

  // 当初始列发生变化时重新初始化 - 使用 useEffect 但避免无限循环
  useEffect(() => {
    setColumnWidths(prev => {
      // 检查是否需要更新：比较列的数量和字段名
      const prevFields = Array.from(prev.keys()).sort();
      const newFields = initialColumns.map(col => col.fieldName).sort();

      // 如果列没有变化，不更新状态
      if (prevFields.length === newFields.length &&
          prevFields.every((field, _index) => field === newFields[index])) {
        return prev;
      }

      // 列发生了变化，返回新的初始化状态
      return initializeColumnWidths;
    });
  }, [initialColumns, initializeColumnWidths]);

  return {
    columnWidths,
    isResizing,
    resizingColumn,
    handleMouseDown,
    getColumnStyle,
    resetColumnWidths
  };
};
