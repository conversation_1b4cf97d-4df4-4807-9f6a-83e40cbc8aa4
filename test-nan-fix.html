<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test NaN Fix</title>
</head>
<body>
    <h1>Testing NaN Fix for us_pmn Database</h1>
    
    <div id="test-results">
        <h2>Test Results:</h2>
        <div id="results"></div>
    </div>

    <script>
        async function testNaNFix() {
            const results = document.getElementById('results');
            
            try {
                // Test 1: Check API response
                results.innerHTML += '<p>🔍 Testing API response...</p>';
                const response = await fetch('/api/data/us_pmn?page=1&limit=20');
                const data = await response.json();
                
                if (data.success && data.pagination) {
                    const { totalCount, totalPages, maxPages } = data.pagination;
                    results.innerHTML += `<p>✅ API Response: totalCount=${totalCount}, totalPages=${totalPages}, maxPages=${maxPages}</p>`;
                    
                    // Test 2: Check Math.min calculation
                    const maxValue = Math.min(totalPages || 1, maxPages || 100);
                    results.innerHTML += `<p>✅ Math.min calculation: ${maxValue} (should not be NaN)</p>`;
                    
                    if (isNaN(maxValue)) {
                        results.innerHTML += '<p>❌ ERROR: Math.min result is NaN!</p>';
                    } else {
                        results.innerHTML += '<p>✅ Math.min result is valid number</p>';
                    }
                } else {
                    results.innerHTML += '<p>❌ API request failed</p>';
                }
                
                // Test 3: Test with edge cases
                results.innerHTML += '<p>🧪 Testing edge cases...</p>';
                
                const testCases = [
                    { totalPages: 0, maxPages: 100, expected: 100 },
                    { totalPages: undefined, maxPages: 100, expected: 100 },
                    { totalPages: null, maxPages: 100, expected: 100 },
                    { totalPages: 50, maxPages: undefined, expected: 100 },
                    { totalPages: 50, maxPages: null, expected: 100 },
                    { totalPages: 50, maxPages: 100, expected: 50 }
                ];
                
                testCases.forEach((testCase, index) => {
                    const result = Math.min(testCase.totalPages || 1, testCase.maxPages || 100);
                    const passed = result === testCase.expected && !isNaN(result);
                    results.innerHTML += `<p>${passed ? '✅' : '❌'} Test ${index + 1}: ${JSON.stringify(testCase)} → ${result}</p>`;
                });
                
                results.innerHTML += '<p>🎉 All tests completed!</p>';
                
            } catch (error) {
                results.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run tests when page loads
        window.addEventListener('load', testNaNFix);
    </script>
</body>
</html>
